class LuaObfuscatorUI {
    constructor() {
        this.initializeElements();
        this.attachEventListeners();
        this.updateInputStats();
    }

    initializeElements() {
        this.inputCode = document.getElementById('inputCode');
        this.outputCode = document.getElementById('outputCode');
        this.obfuscateBtn = document.getElementById('obfuscateBtn');
        this.copyOutput = document.getElementById('copyOutput');
        this.downloadOutput = document.getElementById('downloadOutput');
        this.clearInput = document.getElementById('clearInput');
        this.loadExample = document.getElementById('loadExample');
        this.inputStats = document.getElementById('inputStats');
        this.outputStats = document.getElementById('outputStats');
        this.compressionRatio = document.getElementById('compressionRatio');
        this.notification = document.getElementById('notification');
    }

    attachEventListeners() {
        this.obfuscateBtn.addEventListener('click', () => this.obfuscateCode());
        this.copyOutput.addEventListener('click', () => this.copyToClipboard());
        this.downloadOutput.addEventListener('click', () => this.downloadCode());
        this.clearInput.addEventListener('click', () => this.clearInput());
        this.loadExample.addEventListener('click', () => this.loadExampleCode());
        this.inputCode.addEventListener('input', () => this.updateInputStats());
        this.inputCode.addEventListener('keydown', (e) => this.handleKeyDown(e));
    }

    updateInputStats() {
        const code = this.inputCode.value;
        const lines = code.split('\n').length;
        const chars = code.length;
        this.inputStats.textContent = `Lines: ${lines} | Characters: ${chars}`;
    }

    handleKeyDown(e) {
        // Tab key support
        if (e.key === 'Tab') {
            e.preventDefault();
            const start = this.inputCode.selectionStart;
            const end = this.inputCode.selectionEnd;
            const value = this.inputCode.value;
            
            this.inputCode.value = value.substring(0, start) + '    ' + value.substring(end);
            this.inputCode.selectionStart = this.inputCode.selectionEnd = start + 4;
        }
    }

    async obfuscateCode() {
        const code = this.inputCode.value.trim();
        
        if (!code) {
            this.showNotification('Please enter some Lua code to obfuscate', 'error');
            return;
        }

        this.setLoading(true);

        try {
            const response = await fetch('/api/obfuscate', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ code })
            });

            const result = await response.json();

            if (result.success) {
                this.outputCode.value = result.obfuscated;
                this.updateOutputStats(result);
                this.copyOutput.disabled = false;
                this.downloadOutput.disabled = false;
                this.showNotification('Code obfuscated successfully!', 'success');
            } else {
                this.showNotification(`Obfuscation failed: ${result.error}`, 'error');
                this.outputCode.value = '';
                this.outputStats.textContent = 'Obfuscation failed';
                this.copyOutput.disabled = true;
                this.downloadOutput.disabled = true;
            }
        } catch (error) {
            this.showNotification(`Network error: ${error.message}`, 'error');
            this.outputCode.value = '';
            this.outputStats.textContent = 'Network error';
            this.copyOutput.disabled = true;
            this.downloadOutput.disabled = true;
        } finally {
            this.setLoading(false);
        }
    }

    updateOutputStats(result) {
        const reduction = ((result.originalSize - result.obfuscatedSize) / result.originalSize * 100).toFixed(1);
        const ratio = (result.obfuscatedSize / result.originalSize).toFixed(2);
        
        this.outputStats.textContent = `Characters: ${result.obfuscatedSize}`;
        
        if (result.obfuscatedSize < result.originalSize) {
            this.compressionRatio.textContent = `${reduction}% smaller`;
            this.compressionRatio.style.color = '#28a745';
        } else {
            const increase = ((result.obfuscatedSize - result.originalSize) / result.originalSize * 100).toFixed(1);
            this.compressionRatio.textContent = `${increase}% larger`;
            this.compressionRatio.style.color = '#dc3545';
        }
    }

    async copyToClipboard() {
        try {
            await navigator.clipboard.writeText(this.outputCode.value);
            this.showNotification('Obfuscated code copied to clipboard!', 'success');
        } catch (error) {
            // Fallback for older browsers
            this.outputCode.select();
            document.execCommand('copy');
            this.showNotification('Obfuscated code copied to clipboard!', 'success');
        }
    }

    downloadCode() {
        const code = this.outputCode.value;
        const blob = new Blob([code], { type: 'text/plain' });
        const url = URL.createObjectURL(blob);
        
        const a = document.createElement('a');
        a.href = url;
        a.download = 'obfuscated_script.lua';
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        
        this.showNotification('Obfuscated code downloaded!', 'success');
    }

    clearInput() {
        this.inputCode.value = '';
        this.outputCode.value = '';
        this.updateInputStats();
        this.outputStats.textContent = 'Ready to obfuscate';
        this.compressionRatio.textContent = '';
        this.copyOutput.disabled = true;
        this.downloadOutput.disabled = true;
        this.inputCode.focus();
    }

    loadExampleCode() {
        const exampleCode = `-- Example Lua Script
print("Welcome to Lua Obfuscator!")

-- Variables and basic operations
local name = "User"
local age = 25
local isActive = true

-- Function definition
function greetUser(userName, userAge)
    if userAge >= 18 then
        print("Hello, " .. userName .. "! You are " .. userAge .. " years old.")
        print("You are an adult.")
    else
        print("Hello, " .. userName .. "! You are " .. userAge .. " years old.")
        print("You are a minor.")
    end
end

-- Table operations
local userData = {
    name = name,
    age = age,
    active = isActive,
    skills = {"Lua", "Programming", "Scripting"}
}

-- Loop through table
print("User skills:")
for i, skill in ipairs(userData.skills) do
    print(i .. ". " .. skill)
end

-- Call function
greetUser(userData.name, userData.age)

-- Mathematical operations
local x, y = 10, 20
print("Sum: " .. (x + y))
print("Product: " .. (x * y))

-- Conditional logic
if userData.active then
    print("User is currently active")
else
    print("User is inactive")
end`;

        this.inputCode.value = exampleCode;
        this.updateInputStats();
        this.showNotification('Example code loaded!', 'info');
    }

    setLoading(loading) {
        const btnText = this.obfuscateBtn.querySelector('.btn-text');
        const btnLoading = this.obfuscateBtn.querySelector('.btn-loading');
        
        if (loading) {
            btnText.style.display = 'none';
            btnLoading.style.display = 'inline';
            this.obfuscateBtn.disabled = true;
        } else {
            btnText.style.display = 'inline';
            btnLoading.style.display = 'none';
            this.obfuscateBtn.disabled = false;
        }
    }

    showNotification(message, type = 'info') {
        this.notification.textContent = message;
        this.notification.className = `notification ${type}`;
        this.notification.classList.add('show');
        
        setTimeout(() => {
            this.notification.classList.remove('show');
        }, 3000);
    }
}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new LuaObfuscatorUI();
});

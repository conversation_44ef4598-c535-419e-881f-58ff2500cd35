// Test script to validate the Lua obfuscation functionality
const fetch = require('node-fetch');

async function testObfuscation() {
    const testCases = [
        {
            name: "Simple Hello World",
            code: `print("Hello, World!")`
        },
        {
            name: "Variables and Math",
            code: `local x = 10
local y = 20
print("Sum:", x + y)
print("Product:", x * y)`
        },
        {
            name: "Function Definition",
            code: `function greet(name)
    print("Hello, " .. name .. "!")
end

greet("User")`
        },
        {
            name: "Table Operations",
            code: `local data = {
    name = "Test",
    value = 42,
    items = {1, 2, 3}
}

for i, v in ipairs(data.items) do
    print(i, v)
end`
        },
        {
            name: "Conditional Logic",
            code: `local age = 25
if age >= 18 then
    print("Adult")
else
    print("Minor")
end`
        }
    ];

    console.log("🔒 Testing Lua Obfuscation System\n");

    for (const testCase of testCases) {
        console.log(`Testing: ${testCase.name}`);
        console.log("=" .repeat(50));
        
        try {
            const response = await fetch('http://localhost:3000/api/obfuscate', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ code: testCase.code })
            });

            const result = await response.json();

            if (result.success) {
                console.log("✅ Obfuscation successful!");
                console.log(`📊 Original size: ${result.originalSize} characters`);
                console.log(`📊 Obfuscated size: ${result.obfuscatedSize} characters`);
                
                const ratio = (result.obfuscatedSize / result.originalSize).toFixed(2);
                console.log(`📊 Size ratio: ${ratio}x`);
                
                console.log("\n📝 Original code:");
                console.log(testCase.code);
                
                console.log("\n🔐 Obfuscated code (first 200 chars):");
                console.log(result.obfuscated.substring(0, 200) + "...");
                
                // Check if obfuscated code contains no readable strings
                const hasReadableStrings = /print|local|function|if|then|else|end/.test(result.obfuscated);
                if (!hasReadableStrings) {
                    console.log("✅ No readable Lua keywords found in output");
                } else {
                    console.log("⚠️  Some readable keywords still present");
                }
                
            } else {
                console.log("❌ Obfuscation failed:", result.error);
            }
        } catch (error) {
            console.log("❌ Network error:", error.message);
        }
        
        console.log("\n" + "=".repeat(50) + "\n");
    }
}

// Run tests if server is available
testObfuscation().catch(console.error);

# 🔒 Lua Script Obfuscator

A powerful web application for obfuscating Lua scripts with multi-layer encryption and complex pattern generation. This tool transforms readable Lua code into heavily obfuscated versions that are extremely difficult to reverse engineer while maintaining full functionality.

## ✨ Features

- **Multi-Layer Obfuscation**: Base64 encoding + XOR encryption with random keys
- **Complex Pattern Generation**: Difficult-to-understand code structures and execution patterns
- **Complete Minification**: Removes all comments, whitespace, and unnecessary characters
- **Embedded Decoder**: Self-contained decoder within the obfuscated output
- **Web Interface**: User-friendly interface with copy and download functionality
- **Real-time Processing**: Fast obfuscation with immediate results

## 🚀 Quick Start

### Prerequisites
- Node.js (version 14 or higher)
- npm or yarn package manager

### Installation

1. Clone or download this repository
2. Navigate to the project directory
3. Install dependencies:
```bash
npm install
```

### Running the Application

Start the server:
```bash
npm start
```

Or for development:
```bash
npm run dev
```

The application will be available at: `http://localhost:3000`

## 🛠️ How It Works

### Obfuscation Process

1. **Code Cleaning**: Removes comments and normalizes whitespace
2. **Base64 Encoding**: Encodes the cleaned Lua code to Base64
3. **XOR Encryption**: Applies XOR encryption with a randomly generated 24-character key
4. **Pattern Generation**: Creates complex execution patterns with dummy code injection
5. **Decoder Embedding**: Embeds a self-contained decoder within the output
6. **Minification**: Removes all unnecessary whitespace for maximum compression

### Security Features

- **Random Variable Names**: All variables use randomly generated names
- **Dummy Code Injection**: Adds confusing dummy operations
- **Complex Execution Patterns**: Wraps code in multiple layers of function calls
- **No Comments**: Final output contains no explanatory text
- **Minimal Whitespace**: Compressed to single-line format

## 📖 Usage

### Web Interface

1. Open the application in your browser
2. Paste your Lua code in the input area
3. Click "Obfuscate Code" to process
4. Copy or download the obfuscated result

### Example

**Original Code:**
```lua
print("Hello, World!")
local x = 10
local y = 20
print("Sum:", x + y)
```

**Obfuscated Output:**
```lua
local ___aB3={847,234}local ___xY9={108,111,99,97,108,32,120,32,61,32,49,48,10,108,111,99,97,108,32,121,32,61,32,50,48,10,112,114,105,110,116,40,34,83,117,109,58,34,44,32,120,32,43,32,121,41}local ___mN7={65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80}___aB3[1]=___aB3[1]+___aB3[2]local ___kL5=""for ___pQ1=1,#___xY9 do local ___rT8=string.char(___xY9[___pQ1]~___mN7[((___pQ1-1)%#___mN7)+1])___kL5=___kL5..___rT8 end;local function ___wE2(___vU4)local b='ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/'___vU4=string.gsub(___vU4,'[^'..b..'=]','')return(___vU4:gsub('.',function(x)if(x=='=')then return''end;local r,f='',(b:find(x)-1)for ___iO6=6,1,-1 do r=r..(f%2^___iO6-f%2^(___iO6-1)>0 and'1'or'0')end;return r end):gsub('%d%d%d?%d?%d?%d?%d?%d?',function(x)if(#x~=8)then return''end;local c=0;for ___iO6=1,8 do c=c+(x:sub(___iO6,___iO6)=='1'and 2^(8-___iO6)or 0)end;return string.char(c)end))end;___sH3=___wE2(___kL5)local ___gF1=load(___sH3)___gF1()
```

## 🔧 API Endpoints

### POST /api/obfuscate

Obfuscates the provided Lua code.

**Request Body:**
```json
{
  "code": "print('Hello, World!')"
}
```

**Response:**
```json
{
  "success": true,
  "obfuscated": "local ___aB3={...}",
  "originalSize": 22,
  "obfuscatedSize": 1247
}
```

## ⚠️ Important Notes

- **Always test your obfuscated code** before deployment
- **Keep a backup** of your original source code
- **Performance impact**: Obfuscation may slightly affect execution speed
- **Legitimate use only**: This tool is for legitimate code protection purposes

## 🛡️ Security Considerations

This obfuscator provides strong protection against casual reverse engineering but should not be considered unbreakable. For maximum security:

1. Use in combination with other protection methods
2. Regularly update your obfuscation patterns
3. Monitor for potential security vulnerabilities
4. Consider server-side execution for critical code

## 📝 License

This project is licensed under the ISC License.

## 🤝 Contributing

Contributions are welcome! Please feel free to submit issues and enhancement requests.

---

**Built with ❤️ for secure Lua code protection**

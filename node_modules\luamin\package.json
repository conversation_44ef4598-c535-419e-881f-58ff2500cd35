{"name": "luamin", "version": "1.0.4", "description": "A Lua minifier written in JavaScript", "homepage": "https://mths.be/luamin", "main": "luamin.js", "bin": "bin/luamin", "keywords": ["lua", "minify", "minifier"], "license": "MIT", "author": {"name": "<PERSON>", "url": "https://mathiasbynens.be/"}, "repository": {"type": "git", "url": "https://github.com/mathiasbynens/luamin.git"}, "bugs": "https://github.com/mathiasbynens/luamin/issues", "files": ["LICENSE-MIT.txt", "luamin.js", "bin/", "man/"], "directories": {"bin": "bin", "man": "man", "test": "tests"}, "scripts": {"test": "node tests/tests.js"}, "dependencies": {"luaparse": "^0.2.1"}, "devDependencies": {"grunt": "^0.4.4", "grunt-shell": "^1.1.2", "istanbul": "^0.4.2", "qunit-extras": "^1.4.5", "qunitjs": "~1.11.0", "requirejs": "^2.1.22"}}
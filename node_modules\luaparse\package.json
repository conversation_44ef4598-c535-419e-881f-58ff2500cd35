{"name": "luapar<PERSON>", "version": "0.2.1", "description": "A Lua parser in JavaScript", "keywords": ["ast", "lua", "parser", "parsing"], "homepage": "https://oxyc.github.io/luaparse/", "bugs": "https://github.com/oxyc/luaparse/issues", "license": "MIT", "author": "<PERSON><PERSON> <<EMAIL>> (http://www.oxy.fi/)", "files": ["README.md", "LICENSE", "luaparse.js", "bin/luaparse"], "main": "luaparse.js", "bin": "./bin/luaparse", "man": "./docs/luaparse.1", "repository": {"type": "git", "url": "https://github.com/oxyc/luaparse.git"}, "scripts": {"test": "make qa"}, "devDependencies": {"benchmark": "~1.0.0", "complexity-report": "~0.10.5", "docco": "~0.6.3", "gulp": "~3.8.10", "gulp-add-src": "^0.2.0", "gulp-filelog": "^0.4.1", "gulp-header": "^1.2.2", "gulp-jshint": "^1.9.0", "gulp-rename": "^1.2.0", "gulp-striphtml": "0.0.1", "gulp-uglify": "^1.0.1", "istanbul": "~0.3.2", "marked": "~0.3.2", "spec": "git://github.com/kitcambridge/spec.git#gh-pages", "testem": "~0.6.9"}}